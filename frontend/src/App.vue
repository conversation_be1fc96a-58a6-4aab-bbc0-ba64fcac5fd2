<script setup lang="ts">
import { RouterLink, RouterView, useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'
import ConnectionStatus from './components/ConnectionStatus.vue'

const authStore = useAuthStore()
const router = useRouter()

const logout = async () => {
  await authStore.logout()
  router.push('/login')
}
</script>

<template>
  <div class="app-container">
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img alt="Vue logo" src="@/assets/logo.svg" width="40" height="40" />
          <h1>My App</h1>
        </div>

        <nav class="nav">
          <RouterLink to="/" class="nav-link">Home</RouterLink>
          <RouterLink to="/about" class="nav-link">About</RouterLink>
          <template v-if="authStore.isAuthenticated">
            <RouterLink to="/dashboard" class="nav-link">Dashboard</RouterLink>
            <a href="#" @click.prevent="logout" class="nav-link">Logout</a>
          </template>
          <template v-else>
            <RouterLink to="/login" class="nav-link">Login</RouterLink>
            <RouterLink to="/register" class="nav-link">Register</RouterLink>
          </template>
        </nav>

        <ConnectionStatus class="connection-status" />
      </div>
    </header>

    <main class="content">
      <div class="content-inner">
        <RouterView />
      </div>
    </main>
  </div>
</template>

<style>
/* Reset CSS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: Arial, sans-serif;
  background-color: #f8f8f8;
  color: #333;
}

#app {
  width: 100%;
  min-height: 100%;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.nav {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
}

.nav-link:hover {
  color: #42b883;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #42b883;
  transition: width 0.3s;
}

.nav-link:hover::after,
.router-link-active::after {
  width: 100%;
}

.router-link-active {
  color: #42b883;
}

.content {
  flex: 1;
  width: 100%;
  background-color: white;
  display: flex;
  justify-content: center;
}

.content-inner {
  max-width: 1400px;
  width: 100%;
  padding: 2rem;
  box-sizing: border-box;
}

.connection-status {
  margin-left: 1rem;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    padding: 1rem;
  }

  .logo {
    margin-bottom: 1rem;
  }

  .nav {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .connection-status {
    margin-left: 0;
    align-self: flex-end;
  }

  .content-inner {
    padding: 1rem;
  }
}
</style>
